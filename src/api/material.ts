import request from '@/utils/request'
import type {
  Material,
  MaterialListResponse,
  QueryMaterialParams,
  CreateMaterialParams,
  UpdateMaterialParams,
  ImportResponse,
  ApiResponse,
} from '@/types/bom'

/**
 * 获取物料列表
 * @param params 查询参数
 * @returns 物料列表和总数
 */
export function getMaterialList(params?: QueryMaterialParams) {
  return request<ApiResponse<MaterialListResponse>>({
    url: '/api/material',
    method: 'get',
    params,
  })
}

/**
 * 获取物料详情
 * @param id 物料ID
 * @returns 物料详情
 */
export function getMaterialById(id: string) {
  return request<ApiResponse<Material>>({
    url: `/api/material/${id}`,
    method: 'get',
  })
}

/**
 * 创建物料
 * @param data 物料数据
 * @returns 创建结果
 */
export function createMaterial(data: CreateMaterialParams) {
  return request<ApiResponse<Material>>({
    url: '/api/material',
    method: 'post',
    data,
  })
}

/**
 * 更新物料
 * @param id 物料ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateMaterial(id: string, data: UpdateMaterialParams) {
  return request<ApiResponse<Material>>({
    url: `/api/material/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除物料
 * @param id 物料ID
 * @returns 删除结果
 */
export function deleteMaterial(id: string) {
  return request<ApiResponse<void>>({
    url: `/api/material/${id}`,
    method: 'delete',
  })
}

/**
 * 获取年份选项
 * @returns 年份列表
 */
export function getMaterialYearOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/material/options/years',
    method: 'get',
  })
}

/**
 * 获取单位选项
 * @returns 单位列表
 */
export function getMaterialUnitOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/material/options/units',
    method: 'get',
  })
}

/**
 * 生成物料编号
 * @param categoryId 分类ID
 * @returns 生成的物料编号
 */
export function generateMaterialId(categoryId: string) {
  return request<ApiResponse<{ material_id: string }>>({
    url: '/api/material/generate-id',
    method: 'get',
    params: { categoryId },
  })
}

/**
 * 批量导入物料数据
 * @param data 物料数据列表
 * @returns 导入结果
 */
export function importMaterialBatch(data: Partial<Material>[]) {
  return request<ApiResponse<ImportResponse>>({
    url: '/api/material/import-batch',
    method: 'post',
    data,
    timeout: 60000,
  })
}

/**
 * 导入JSON数据
 * @param importData 包含: sheetName, data, fileName, totalRecords
 * @returns 导入结果
 */
export const importMaterialJsonData = async (importData: any) => {
  try {
    return request<ApiResponse<ImportResponse>>({
      url: '/api/material/import-json',
      method: 'post',
      data: importData,
      timeout: 60000,
    })
  } catch (error) {
    console.error('导入物料数据时出错:', error)
    throw error
  }
}
